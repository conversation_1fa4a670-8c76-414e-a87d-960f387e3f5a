#!/usr/bin/env python3
"""
Quick Training Script for Lyrics Generation Model
A simplified version for demonstration and quick setup
"""

import json
import torch
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling
)
from peft import LoraConfig, get_peft_model, TaskType
from torch.utils.data import Dataset
import os

class SimpleLyricsDataset(Dataset):
    def __init__(self, data, tokenizer, max_length=256):
        self.data = data
        self.tokenizer = tokenizer
        self.max_length = max_length
        
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        text = item['prompt']
        
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': encoding['input_ids'].flatten()
        }

def quick_train():
    """Quick training function for demonstration"""
    print("🎵 Starting Quick Training for Lyrics Generator")
    
    # Check if training data exists
    if not os.path.exists('lyrics_training_dataset.json'):
        print("❌ Training data not found. Please run prepare_training_data.py first")
        return
    
    # Load training data
    print("📊 Loading training data...")
    with open('lyrics_training_dataset.json', 'r', encoding='utf-8') as f:
        training_data = json.load(f)
    
    print(f"✅ Loaded {len(training_data)} training examples")
    
    # Use a smaller, faster model for quick training
    model_name = "distilgpt2"  # Smaller and faster than DialoGPT
    
    print(f"🤖 Loading model: {model_name}")
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModelForCausalLM.from_pretrained(model_name)
    
    # Add special tokens
    special_tokens = {
        "pad_token": "<pad>",
        "additional_special_tokens": [
            "<LOVE_STORY>", "</LOVE_STORY>",
            "<LYRICS>", "</LYRICS>",
            "<STYLE>", "</STYLE>"
        ]
    }
    
    if tokenizer.pad_token is None:
        tokenizer.add_special_tokens({"pad_token": "<pad>"})
    
    tokenizer.add_special_tokens(special_tokens)
    model.resize_token_embeddings(len(tokenizer))
    
    print(f"📝 Vocabulary size: {len(tokenizer)}")
    
    # Setup LoRA for efficient training
    print("⚙️ Setting up LoRA configuration...")
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        inference_mode=False,
        r=4,  # Very small rank for quick training
        lora_alpha=8,
        lora_dropout=0.1,
        target_modules=["c_attn", "c_proj"]
    )
    
    model = get_peft_model(model, lora_config)
    model.print_trainable_parameters()
    
    # Prepare dataset (use only first 20 examples for quick training)
    print("🔄 Preparing dataset...")
    quick_data = training_data[:20]  # Use only 20 examples for speed
    
    train_dataset = SimpleLyricsDataset(quick_data, tokenizer)
    
    # Training arguments for quick training
    training_args = TrainingArguments(
        output_dir="./quick_lyrics_model",
        num_train_epochs=1,  # Just 1 epoch for demo
        per_device_train_batch_size=2,
        gradient_accumulation_steps=2,
        warmup_steps=5,
        max_steps=20,  # Very few steps for quick demo
        learning_rate=5e-4,
        logging_steps=5,
        save_steps=10,
        save_strategy="steps",
        remove_unused_columns=False,
        dataloader_pin_memory=False,
        fp16=False,
    )
    
    # Data collator
    data_collator = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm=False,
    )
    
    # Initialize trainer
    print("🏋️ Initializing trainer...")
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        data_collator=data_collator,
        tokenizer=tokenizer,
    )
    
    # Start training
    print("🚀 Starting training...")
    trainer.train()
    
    # Save the model
    print("💾 Saving model...")
    trainer.save_model("./quick_lyrics_model")
    tokenizer.save_pretrained("./quick_lyrics_model")
    
    print("✅ Quick training completed!")
    print("📁 Model saved to: ./quick_lyrics_model")
    
    # Test the model
    test_model()

def test_model():
    """Test the trained model with a sample input"""
    print("\n🧪 Testing the trained model...")
    
    try:
        # Load the trained model
        tokenizer = AutoTokenizer.from_pretrained("./quick_lyrics_model")
        model = AutoModelForCausalLM.from_pretrained("./quick_lyrics_model")
        
        # Test prompt
        test_prompt = """<LOVE_STORY>We met at a bookstore and bonded over poetry</LOVE_STORY>

<STYLE>romantic, dreamy</STYLE>

<LYRICS>
Hindi:"""
        
        print("📝 Test prompt:")
        print(test_prompt)
        
        # Generate
        inputs = tokenizer.encode(test_prompt, return_tensors='pt')
        
        with torch.no_grad():
            outputs = model.generate(
                inputs,
                max_length=inputs.shape[1] + 50,
                num_return_sequences=1,
                temperature=0.8,
                do_sample=True,
                pad_token_id=tokenizer.pad_token_id
            )
        
        generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        print("\n🎵 Generated output:")
        print("-" * 50)
        print(generated_text)
        print("-" * 50)
        
    except Exception as e:
        print(f"❌ Error testing model: {e}")

def main():
    """Main function"""
    print("🎵 AI Lyrics Generator - Quick Training")
    print("=" * 50)
    
    # Check if we have the required files
    required_files = [
        'lyrics_training_dataset.json',
        'training_data.json'
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print("❌ Missing required files:")
        for file in missing_files:
            print(f"   - {file}")
        print("\n📋 Please run the following commands first:")
        print("   python3 analyze_data.py")
        print("   python3 prepare_training_data.py")
        return
    
    # Check if PyTorch is available
    if not torch.cuda.is_available():
        print("⚠️  CUDA not available. Training will use CPU (slower)")
    else:
        print(f"🚀 CUDA available. Using GPU: {torch.cuda.get_device_name()}")
    
    # Start quick training
    quick_train()
    
    print("\n🎉 Quick training demonstration completed!")
    print("💡 For full training, use: python3 fine_tune_model.py")

if __name__ == "__main__":
    main()
