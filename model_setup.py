#!/usr/bin/env python3
"""
Model Setup and Configuration for Lyrics Generation
Sets up the base model and prepares for fine-tuning
"""

import torch
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM, 
    BitsAndBytesConfig,
    TrainingArguments
)
from peft import LoraConfig, get_peft_model, TaskType
import json
import os
from pathlib import Path

class LyricsModelSetup:
    def __init__(self, model_name="Qwen/Qwen2.5-1.5B-Instruct", use_quantization=True):
        """
        Initialize the model setup
        
        Args:
            model_name: Base model to use for fine-tuning
            use_quantization: Whether to use 4-bit quantization for memory efficiency
        """
        self.model_name = model_name
        self.use_quantization = use_quantization
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        print(f"Using device: {self.device}")
        print(f"Base model: {model_name}")
        
    def setup_quantization_config(self):
        """Setup 4-bit quantization configuration"""
        if not self.use_quantization:
            return None
            
        return BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_use_double_quant=True,
            bnb_4bit_quant_type="nf4",
            bnb_4bit_compute_dtype=torch.bfloat16
        )
    
    def load_model_and_tokenizer(self):
        """Load the base model and tokenizer"""
        print("Loading tokenizer...")
        self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
        
        # Add special tokens for lyrics generation
        special_tokens = {
            "pad_token": "<pad>",
            "eos_token": "</s>",
            "bos_token": "<s>",
            "unk_token": "<unk>",
            "additional_special_tokens": [
                "<LOVE_STORY>", "</LOVE_STORY>",
                "<LYRICS>", "</LYRICS>",
                "<STYLE>", "</STYLE>",
                "<HINDI>", "</HINDI>",
                "<ENGLISH>", "</ENGLISH>"
            ]
        }
        
        # Add tokens that don't exist
        tokens_to_add = {}
        for key, value in special_tokens.items():
            if key == "additional_special_tokens":
                continue
            if getattr(self.tokenizer, key, None) is None:
                tokens_to_add[key] = value
        
        if tokens_to_add:
            self.tokenizer.add_special_tokens(tokens_to_add)
        
        # Add additional special tokens
        self.tokenizer.add_special_tokens({
            "additional_special_tokens": special_tokens["additional_special_tokens"]
        })
        
        print("Loading model...")
        quantization_config = self.setup_quantization_config()
        
        self.model = AutoModelForCausalLM.from_pretrained(
            self.model_name,
            quantization_config=quantization_config,
            device_map="auto" if self.device == "cuda" else None,
            torch_dtype=torch.bfloat16 if self.device == "cuda" else torch.float32,
            trust_remote_code=True
        )
        
        # Resize token embeddings to accommodate new tokens
        self.model.resize_token_embeddings(len(self.tokenizer))
        
        print(f"Model loaded successfully. Vocab size: {len(self.tokenizer)}")
        
        return self.model, self.tokenizer
    
    def setup_lora_config(self):
        """Setup LoRA configuration for efficient fine-tuning"""
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            inference_mode=False,
            r=16,  # Rank
            lora_alpha=32,  # Alpha parameter
            lora_dropout=0.1,  # Dropout probability
            target_modules=[
                "q_proj", "k_proj", "v_proj", "o_proj",
                "gate_proj", "up_proj", "down_proj"
            ] if "llama" in self.model_name.lower() else ["c_attn", "c_proj"]
        )
        
        return lora_config
    
    def prepare_model_for_training(self):
        """Prepare the model for LoRA fine-tuning"""
        lora_config = self.setup_lora_config()
        
        # Apply LoRA to the model
        self.model = get_peft_model(self.model, lora_config)
        
        # Print trainable parameters
        self.model.print_trainable_parameters()
        
        return self.model
    
    def get_training_arguments(self, output_dir="./lyrics_model_output"):
        """Get training arguments for fine-tuning"""
        return TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=3,
            per_device_train_batch_size=4,
            per_device_eval_batch_size=4,
            gradient_accumulation_steps=4,
            warmup_steps=100,
            max_steps=1000,
            learning_rate=2e-4,
            fp16=True if self.device == "cuda" else False,
            logging_steps=10,
            save_steps=100,
            eval_steps=100,
            evaluation_strategy="steps",
            save_strategy="steps",
            load_best_model_at_end=True,
            metric_for_best_model="eval_loss",
            greater_is_better=False,
            report_to="wandb" if os.getenv("WANDB_API_KEY") else None,
            run_name="lyrics_generation_finetuning",
            remove_unused_columns=False,
            dataloader_pin_memory=False,
        )

def test_model_setup():
    """Test the model setup"""
    print("Testing model setup...")
    
    # Try different models based on availability
    models_to_try = [
        "microsoft/DialoGPT-medium",
        "microsoft/DialoGPT-small",
        "gpt2",
        "distilgpt2"
    ]
    
    for model_name in models_to_try:
        try:
            print(f"\nTrying model: {model_name}")
            setup = LyricsModelSetup(model_name=model_name, use_quantization=False)
            model, tokenizer = setup.load_model_and_tokenizer()
            
            # Test tokenization
            test_text = "<LOVE_STORY>Boy meets girl in college</LOVE_STORY><LYRICS>"
            tokens = tokenizer.encode(test_text)
            decoded = tokenizer.decode(tokens)
            
            print(f"Test tokenization successful!")
            print(f"Original: {test_text}")
            print(f"Decoded: {decoded}")
            
            # Test model preparation
            model = setup.prepare_model_for_training()
            print(f"Model preparation successful!")
            
            return setup, model, tokenizer
            
        except Exception as e:
            print(f"Failed to load {model_name}: {e}")
            continue
    
    raise Exception("Could not load any model")

if __name__ == "__main__":
    try:
        setup, model, tokenizer = test_model_setup()
        print("\nModel setup completed successfully!")
        
        # Save configuration
        config = {
            "model_name": setup.model_name,
            "vocab_size": len(tokenizer),
            "device": setup.device,
            "special_tokens": tokenizer.special_tokens_map
        }
        
        with open("model_config.json", "w") as f:
            json.dump(config, f, indent=2)
        
        print("Configuration saved to model_config.json")
        
    except Exception as e:
        print(f"Model setup failed: {e}")
        print("Please install required dependencies: pip install -r requirements.txt")
