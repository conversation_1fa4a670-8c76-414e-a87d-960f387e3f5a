# AI Lyrics Generator for Suno

A sophisticated AI-powered lyrics generator that transforms love stories into beautiful Hindi and English song lyrics, complete with Suno-compatible style tags. The system uses a fine-tuned language model trained on a dataset of 4,230+ Hindi love songs.

## 🎵 Features

- **Love Story to Lyrics**: Transform personal love stories into poetic song lyrics
- **Bilingual Output**: Generate lyrics in both Hindi and English
- **Style Tag Generation**: Automatic generation of Suno-compatible style tags
- **Fine-tuned AI Model**: Custom model trained on Hindi love song dataset
- **Modern Web Interface**: Beautiful, responsive React/Next.js frontend
- **Real-time Generation**: Fast lyrics generation with loading states
- **Export Options**: Copy to clipboard and download as text file

## 🏗️ Architecture

### Frontend (Next.js + React)
- Modern, responsive web interface
- Real-time lyrics generation
- Style tag suggestions
- Copy/download functionality

### Backend (FastAPI + Python)
- Fine-tuned language model serving
- RESTful API endpoints
- CORS-enabled for frontend integration
- Fallback mock responses

### AI Model
- Base model: Microsoft DialoGPT-medium
- Fine-tuned on 4,230+ Hindi love songs
- LoRA (Low-Rank Adaptation) for efficient training
- Custom tokenization for Hindi/English lyrics

## 📊 Dataset Analysis

- **Total Songs**: 4,230
- **Love-themed Songs**: 4,018 (95%)
- **Average Song Length**: 46 lines
- **Languages**: Hindi with English translations
- **Themes**: Romance, emotion, passion, dreams, nostalgia

## 🚀 Quick Start

### Prerequisites

- Python 3.9+
- Node.js 18+
- 8GB+ RAM (for model training)
- CUDA-compatible GPU (optional, for faster training)

### 1. Clone the Repository

```bash
git clone <repository-url>
cd lyrics-generator-project
```

### 2. Set Up the AI Model

```bash
# Install Python dependencies
pip install -r requirements.txt

# Analyze the dataset
python3 analyze_data.py

# Prepare training data
python3 prepare_training_data.py

# Optional: Fine-tune the model (requires significant compute)
python3 fine_tune_model.py
```

### 3. Start the Backend API

```bash
# Install FastAPI dependencies
pip install fastapi uvicorn

# Start the API server
python3 backend_api.py
```

The API will be available at `http://localhost:8000`

### 4. Start the Frontend

```bash
cd lyrics-generator
npm install
npm run dev
```

The web application will be available at `http://localhost:3000`

## 🎯 Usage

1. **Enter Your Love Story**: Describe your love story in the text area
2. **Select Style Preferences**: Choose from romantic, emotional, upbeat, etc.
3. **Generate Lyrics**: Click the generate button to create your lyrics
4. **Copy or Download**: Use the generated lyrics with Suno or other platforms

### Example Input:
```
We met at a small coffee shop on a rainy evening. She was reading a book about poetry, and I couldn't help but ask about it. We talked for hours about literature, dreams, and life. That conversation changed everything for both of us.
```

### Example Output:
```
Style Tags: romantic, dreamy, acoustic

Hindi Lyrics:
कॉफी की खुशबू में तेरा प्यार मिला
किताबों के बीच में दिल का करार मिला
पहली नज़र में ही जाना था
ये इश्क़ हमारा दीवाना था

English Translation:
In the fragrance of coffee, I found your love
Among the books, my heart found peace
At first sight I knew
This love of ours was crazy
```

## 🔧 API Endpoints

### POST `/generate-lyrics`
Generate lyrics from a love story.

**Request Body:**
```json
{
  "love_story": "Your love story here...",
  "style_preference": "romantic, acoustic"
}
```

**Response:**
```json
{
  "hindi_lyrics": "Generated Hindi lyrics...",
  "english_lyrics": "Generated English lyrics...",
  "style_tags": ["romantic", "acoustic", "dreamy"],
  "success": true,
  "message": "Lyrics generated successfully"
}
```

### GET `/health`
Check API health and model status.

## 🎨 Customization

### Adding New Styles
Edit the `style_keywords` in `backend_api.py`:

```python
self.style_keywords = {
    'your_style': ['keyword1', 'keyword2', 'keyword3'],
    # ... existing styles
}
```

### Modifying the UI
The frontend components are in `lyrics-generator/src/components/`:
- `LyricsGenerator.tsx` - Main generation interface
- `Header.tsx` - Navigation header
- `Footer.tsx` - Footer component

## 🧠 Model Training

### Dataset Preparation
```bash
python3 analyze_data.py  # Analyze the dataset
python3 prepare_training_data.py  # Prepare training prompts
```

### Fine-tuning Process
```bash
python3 fine_tune_model.py  # Start fine-tuning
```

**Training Configuration:**
- Base Model: Microsoft DialoGPT-medium
- Training Method: LoRA (Low-Rank Adaptation)
- Batch Size: 4 (adjustable based on GPU memory)
- Learning Rate: 2e-4
- Max Steps: 1000 (adjustable)

## 📁 Project Structure

```
├── analyze_data.py              # Dataset analysis script
├── prepare_training_data.py     # Training data preparation
├── fine_tune_model.py          # Model fine-tuning script
├── model_setup.py              # Model configuration
├── backend_api.py              # FastAPI backend server
├── requirements.txt            # Python dependencies
├── lyrics-gen/
│   └── clean_data.csv          # Original dataset
├── lyrics-generator/           # Next.js frontend
│   ├── src/
│   │   ├── app/
│   │   │   ├── page.tsx        # Main page
│   │   │   └── api/generate/   # API routes
│   │   └── components/         # React components
│   └── package.json
└── README.md
```

## 🚨 Troubleshooting

### Model Loading Issues
- Ensure you have sufficient RAM (8GB+)
- Check if the model path exists
- Verify Python dependencies are installed

### Frontend Connection Issues
- Ensure backend is running on port 8000
- Check CORS configuration in `backend_api.py`
- Verify API endpoints are accessible

### Performance Issues
- Use GPU for faster inference
- Reduce batch size if running out of memory
- Consider using quantization for lower memory usage

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Dataset sourced from Hindi love songs collection
- Built with Hugging Face Transformers
- UI components inspired by modern design principles
- Special thanks to the open-source community

## 📞 Support

For issues and questions:
- Create an issue on GitHub
- Check the troubleshooting section
- Review the API documentation

---

**Made with ❤️ for music lovers and AI enthusiasts**
