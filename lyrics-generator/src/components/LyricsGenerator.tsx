"use client";

import { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Co<PERSON>,
  Download,
  Refresh<PERSON><PERSON>,
  Heart,
  Music,
} from "lucide-react";

interface GeneratedLyrics {
  lyrics: string;
  language: string;
  style: string[];
}

export function LyricsGenerator() {
  const [loveStory, setLoveStory] = useState("");
  const [stylePreference, setStylePreference] = useState("");
  const [selectedLanguage, setSelectedLanguage] = useState("hindi");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedLyrics, setGeneratedLyrics] =
    useState<GeneratedLyrics | null>(null);

  const styleOptions = [
    "romantic",
    "emotional",
    "upbeat",
    "melancholic",
    "dreamy",
    "passionate",
    "acoustic",
    "pop",
    "ballad",
    "folk",
    "indie",
    "soulful",
    "heartfelt",
  ];

  const handleGenerate = async () => {
    if (!loveStory.trim()) return;

    setIsGenerating(true);

    try {
      const response = await fetch("/api/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          love_story: loveStory,
          style_preference: stylePreference,
          language: selectedLanguage,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        const generatedLyrics: GeneratedLyrics = {
          lyrics: result.lyrics,
          language: result.language,
          style: result.style_tags,
        };

        setGeneratedLyrics(generatedLyrics);
      } else {
        throw new Error(result.message || "Failed to generate lyrics");
      }
    } catch (error) {
      console.error("Error generating lyrics:", error);
      // You could show an error message to the user here
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const downloadLyrics = () => {
    if (!generatedLyrics) return;

    const content = `Love Story: ${loveStory}

Style: ${generatedLyrics.style.join(", ")}
Language: ${generatedLyrics.language}

Generated Lyrics:
${generatedLyrics.lyrics}

Generated by LyricsAI`;

    const blob = new Blob([content], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "generated-lyrics.txt";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-8">
      {/* Input Section */}
      <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
        <div className="space-y-6">
          <div>
            <label
              htmlFor="love-story"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >
              Tell us your love story
            </label>
            <textarea
              id="love-story"
              value={loveStory}
              onChange={(e) => setLoveStory(e.target.value)}
              placeholder="Describe your love story... For example: 'We met at a coffee shop on a rainy day. She was reading a book about poetry, and I couldn't help but ask about it. We talked for hours about literature and dreams...'"
              className="w-full h-32 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 resize-none"
              maxLength={500}
            />
            <div className="text-right text-xs text-gray-500 dark:text-gray-400 mt-1">
              {loveStory.length}/500
            </div>
          </div>

          <div>
            <label
              htmlFor="style"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >
              Style preferences (optional)
            </label>
            <input
              id="style"
              type="text"
              value={stylePreference}
              onChange={(e) => setStylePreference(e.target.value)}
              placeholder="e.g., romantic, acoustic, dreamy"
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
            />
            <div className="mt-2 flex flex-wrap gap-2">
              {styleOptions.map((style) => (
                <button
                  key={style}
                  onClick={() => {
                    const styles = stylePreference
                      .split(",")
                      .map((s) => s.trim())
                      .filter(Boolean);
                    if (styles.includes(style)) {
                      setStylePreference(
                        styles.filter((s) => s !== style).join(", ")
                      );
                    } else {
                      setStylePreference([...styles, style].join(", "));
                    }
                  }}
                  className={`px-3 py-1 text-xs rounded-full border transition-colors ${
                    stylePreference.includes(style)
                      ? "bg-purple-100 dark:bg-purple-900 border-purple-300 dark:border-purple-700 text-purple-700 dark:text-purple-300"
                      : "bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600"
                  }`}
                >
                  {style}
                </button>
              ))}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Language
            </label>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="language"
                  value="hindi"
                  checked={selectedLanguage === "hindi"}
                  onChange={(e) => setSelectedLanguage(e.target.value)}
                  className="mr-2 text-purple-600 focus:ring-purple-500"
                />
                <span className="text-gray-700 dark:text-gray-300">Hindi</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="language"
                  value="english"
                  checked={selectedLanguage === "english"}
                  onChange={(e) => setSelectedLanguage(e.target.value)}
                  className="mr-2 text-purple-600 focus:ring-purple-500"
                />
                <span className="text-gray-700 dark:text-gray-300">
                  English
                </span>
              </label>
            </div>
          </div>

          <button
            onClick={handleGenerate}
            disabled={!loveStory.trim() || isGenerating}
            className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 disabled:from-gray-400 disabled:to-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 disabled:cursor-not-allowed"
          >
            {isGenerating ? (
              <>
                <RefreshCw className="h-5 w-5 animate-spin" />
                <span>Generating lyrics...</span>
              </>
            ) : (
              <>
                <Sparkles className="h-5 w-5" />
                <span>Generate Lyrics</span>
              </>
            )}
          </button>
        </div>
      </div>

      {/* Generated Lyrics Section */}
      {generatedLyrics && (
        <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center space-x-2">
              <Music className="h-6 w-6 text-purple-600" />
              <span>Your Generated Lyrics</span>
            </h2>
            <div className="flex space-x-2">
              <button
                onClick={() => copyToClipboard(generatedLyrics.lyrics)}
                className="p-2 text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 transition-colors"
                title="Copy lyrics"
              >
                <Copy className="h-5 w-5" />
              </button>
              <button
                onClick={downloadLyrics}
                className="p-2 text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 transition-colors"
                title="Download lyrics"
              >
                <Download className="h-5 w-5" />
              </button>
            </div>
          </div>

          <div className="space-y-6">
            {/* Style Tags */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Style Tags for Suno:
              </h3>
              <div className="flex flex-wrap gap-2">
                {generatedLyrics.style.map((style, index) => (
                  <span
                    key={index}
                    className="px-3 py-1 bg-purple-100 dark:bg-purple-900 text-purple-700 dark:text-purple-300 rounded-full text-sm font-medium"
                  >
                    {style}
                  </span>
                ))}
              </div>
            </div>

            {/* Generated Lyrics */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center space-x-2">
                <Heart className="h-5 w-5 text-red-500" />
                <span>
                  {generatedLyrics.language === "hindi" ? "Hindi" : "English"}{" "}
                  Lyrics
                </span>
              </h3>
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <pre className="whitespace-pre-wrap text-gray-800 dark:text-gray-200 font-medium leading-relaxed">
                  {generatedLyrics.lyrics}
                </pre>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
