import { NextRequest, NextResponse } from 'next/server';

interface LyricsRequest {
  love_story: string;
  style_preference?: string;
}

interface LyricsResponse {
  hindi_lyrics: string;
  english_lyrics: string;
  style_tags: string[];
  success: boolean;
  message: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: LyricsRequest = await request.json();
    
    if (!body.love_story?.trim()) {
      return NextResponse.json(
        { error: 'Love story is required' },
        { status: 400 }
      );
    }

    // Try to connect to the Python backend
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:8000';
    
    try {
      const response = await fetch(`${backendUrl}/generate-lyrics`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          love_story: body.love_story,
          style_preference: body.style_preference || '',
        }),
      });

      if (!response.ok) {
        throw new Error(`Backend responded with status: ${response.status}`);
      }

      const result: LyricsResponse = await response.json();
      return NextResponse.json(result);
      
    } catch (backendError) {
      console.error('Backend connection failed:', backendError);
      
      // Fallback to mock response when backend is not available
      const mockResponse = generateMockLyrics(body.love_story, body.style_preference);
      return NextResponse.json(mockResponse);
    }
    
  } catch (error) {
    console.error('Error in generate API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function generateMockLyrics(loveStory: string, stylePreference?: string): LyricsResponse {
  // Extract themes from the love story for better mock generation
  const story = loveStory.toLowerCase();
  
  let mockHindi = '';
  let mockEnglish = '';
  let styles: string[] = [];
  
  // Determine style based on story content
  if (story.includes('coffee') || story.includes('book') || story.includes('library')) {
    styles = ['romantic', 'acoustic', 'dreamy'];
    mockHindi = `कॉफी की खुशबू में तेरा प्यार मिला
किताबों के बीच में दिल का करार मिला
पहली नज़र में ही जाना था
ये इश्क़ हमारा दीवाना था

तेरे साथ बिताए हर लम्हे
सुनहरे ख्वाबों से भी प्यारे हैं
दिल की बात कहने को
शब्द भी कम लगते हैं`;

    mockEnglish = `In the fragrance of coffee, I found your love
Among the books, my heart found peace
At first sight I knew
This love of ours was crazy

Every moment spent with you
Is more precious than golden dreams
To express what's in my heart
Even words seem insufficient`;
  } else if (story.includes('rain') || story.includes('storm') || story.includes('weather')) {
    styles = ['emotional', 'romantic', 'melancholic'];
    mockHindi = `बारिश की बूंदों में तेरी याद आई
हर बूंद के साथ मेरी दुआ आई
भीगे मौसम में तेरा साथ चाहिए
इस दिल को बस तेरा प्यार चाहिए

आसमान से गिरती बारिश की तरह
तू भी मेरी जिंदगी में आया है
हर तूफान को शांत करके
मेरे दिल में प्यार बसाया है`;

    mockEnglish = `In the raindrops, I remembered you
With every drop came my prayer
In this wet weather, I need your company
This heart just needs your love

Like rain falling from the sky
You too have come into my life
Calming every storm
You've settled love in my heart`;
  } else {
    styles = ['romantic', 'heartfelt', 'dreamy'];
    mockHindi = `तेरे साथ चलना है मुझे
हर राह में, हर मोड़ पर
दिल की बात कहना है मुझे
तेरे कानों में, धीरे से

प्यार की ये कहानी है
जो शुरू हुई तुझसे मिलकर
हर दिन नया लगता है
जब तू पास होता है

तेरे बिना अधूरा हूँ मैं
तेरे साथ पूरा हूँ मैं`;

    mockEnglish = `I want to walk with you
On every path, at every turn
I want to tell you what's in my heart
In your ears, softly

This is a story of love
That began when I met you
Every day feels new
When you are near

Without you I am incomplete
With you I am whole`;
  }
  
  // Add user preferences to styles
  if (stylePreference) {
    const userStyles = stylePreference.split(',').map(s => s.trim().toLowerCase());
    styles = [...new Set([...styles, ...userStyles])];
  }
  
  return {
    hindi_lyrics: mockHindi,
    english_lyrics: mockEnglish,
    style_tags: styles,
    success: true,
    message: 'Lyrics generated successfully (mock mode)'
  };
}
