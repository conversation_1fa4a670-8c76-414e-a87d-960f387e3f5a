import { NextRequest, NextResponse } from "next/server";
import {
  generateHindiLyrics,
  generateEnglishLyrics,
  getStyleTags,
  extractStoryElements,
} from "./lyrics-helpers";

interface LyricsRequest {
  love_story: string;
  style_preference?: string;
  language?: string;
}

interface LyricsResponse {
  lyrics: string;
  language: string;
  style_tags: string[];
  success: boolean;
  message: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: LyricsRequest = await request.json();

    if (!body.love_story?.trim()) {
      return NextResponse.json(
        { error: "Love story is required" },
        { status: 400 }
      );
    }

    // Try to connect to the Python backend
    const backendUrl = process.env.BACKEND_URL || "http://localhost:8000";

    try {
      const response = await fetch(`${backendUrl}/generate-lyrics`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          love_story: body.love_story,
          style_preference: body.style_preference || "",
          language: body.language || "hindi",
        }),
      });

      if (!response.ok) {
        throw new Error(`Backend responded with status: ${response.status}`);
      }

      const result: LyricsResponse = await response.json();
      return NextResponse.json(result);
    } catch (backendError) {
      console.error("Backend connection failed:", backendError);

      // Fallback to mock response when backend is not available
      const mockResponse = generateMockLyrics(
        body.love_story,
        body.style_preference,
        body.language
      );
      return NextResponse.json(mockResponse);
    }
  } catch (error) {
    console.error("Error in generate API:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

function generateMockLyrics(
  loveStory: string,
  stylePreference?: string,
  language?: string
): LyricsResponse {
  // Extract themes and keywords from the love story
  const story = loveStory.toLowerCase();
  const selectedLang = language || "hindi";

  // Extract key elements from the story
  const storyElements = extractStoryElements(story);
  let styles: string[] = [];
  let generatedLyrics = "";

  // Generate lyrics based on story themes and selected language
  if (selectedLang === "hindi") {
    generatedLyrics = generateHindiLyrics(story, storyElements);
  } else {
    generatedLyrics = generateEnglishLyrics(story, storyElements);
  }

  styles = getStyleTags(story, stylePreference);

  return {
    lyrics: generatedLyrics,
    language: selectedLang,
    style_tags: styles,
    success: true,
    message: "Lyrics generated successfully (mock mode)",
  };
}
