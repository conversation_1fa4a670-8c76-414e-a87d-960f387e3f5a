{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Amor%20Business/Code/lyrics-generator/src/app/api/generate/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\ninterface LyricsRequest {\n  love_story: string;\n  style_preference?: string;\n}\n\ninterface LyricsResponse {\n  hindi_lyrics: string;\n  english_lyrics: string;\n  style_tags: string[];\n  success: boolean;\n  message: string;\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body: LyricsRequest = await request.json();\n    \n    if (!body.love_story?.trim()) {\n      return NextResponse.json(\n        { error: 'Love story is required' },\n        { status: 400 }\n      );\n    }\n\n    // Try to connect to the Python backend\n    const backendUrl = process.env.BACKEND_URL || 'http://localhost:8000';\n    \n    try {\n      const response = await fetch(`${backendUrl}/generate-lyrics`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          love_story: body.love_story,\n          style_preference: body.style_preference || '',\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Backend responded with status: ${response.status}`);\n      }\n\n      const result: LyricsResponse = await response.json();\n      return NextResponse.json(result);\n      \n    } catch (backendError) {\n      console.error('Backend connection failed:', backendError);\n      \n      // Fallback to mock response when backend is not available\n      const mockResponse = generateMockLyrics(body.love_story, body.style_preference);\n      return NextResponse.json(mockResponse);\n    }\n    \n  } catch (error) {\n    console.error('Error in generate API:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\nfunction generateMockLyrics(loveStory: string, stylePreference?: string): LyricsResponse {\n  // Extract themes from the love story for better mock generation\n  const story = loveStory.toLowerCase();\n  \n  let mockHindi = '';\n  let mockEnglish = '';\n  let styles: string[] = [];\n  \n  // Determine style based on story content\n  if (story.includes('coffee') || story.includes('book') || story.includes('library')) {\n    styles = ['romantic', 'acoustic', 'dreamy'];\n    mockHindi = `कॉफी की खुशबू में तेरा प्यार मिला\nकिताबों के बीच में दिल का करार मिला\nपहली नज़र में ही जाना था\nये इश्क़ हमारा दीवाना था\n\nतेरे साथ बिताए हर लम्हे\nसुनहरे ख्वाबों से भी प्यारे हैं\nदिल की बात कहने को\nशब्द भी कम लगते हैं`;\n\n    mockEnglish = `In the fragrance of coffee, I found your love\nAmong the books, my heart found peace\nAt first sight I knew\nThis love of ours was crazy\n\nEvery moment spent with you\nIs more precious than golden dreams\nTo express what's in my heart\nEven words seem insufficient`;\n  } else if (story.includes('rain') || story.includes('storm') || story.includes('weather')) {\n    styles = ['emotional', 'romantic', 'melancholic'];\n    mockHindi = `बारिश की बूंदों में तेरी याद आई\nहर बूंद के साथ मेरी दुआ आई\nभीगे मौसम में तेरा साथ चाहिए\nइस दिल को बस तेरा प्यार चाहिए\n\nआसमान से गिरती बारिश की तरह\nतू भी मेरी जिंदगी में आया है\nहर तूफान को शांत करके\nमेरे दिल में प्यार बसाया है`;\n\n    mockEnglish = `In the raindrops, I remembered you\nWith every drop came my prayer\nIn this wet weather, I need your company\nThis heart just needs your love\n\nLike rain falling from the sky\nYou too have come into my life\nCalming every storm\nYou've settled love in my heart`;\n  } else {\n    styles = ['romantic', 'heartfelt', 'dreamy'];\n    mockHindi = `तेरे साथ चलना है मुझे\nहर राह में, हर मोड़ पर\nदिल की बात कहना है मुझे\nतेरे कानों में, धीरे से\n\nप्यार की ये कहानी है\nजो शुरू हुई तुझसे मिलकर\nहर दिन नया लगता है\nजब तू पास होता है\n\nतेरे बिना अधूरा हूँ मैं\nतेरे साथ पूरा हूँ मैं`;\n\n    mockEnglish = `I want to walk with you\nOn every path, at every turn\nI want to tell you what's in my heart\nIn your ears, softly\n\nThis is a story of love\nThat began when I met you\nEvery day feels new\nWhen you are near\n\nWithout you I am incomplete\nWith you I am whole`;\n  }\n  \n  // Add user preferences to styles\n  if (stylePreference) {\n    const userStyles = stylePreference.split(',').map(s => s.trim().toLowerCase());\n    styles = [...new Set([...styles, ...userStyles])];\n  }\n  \n  return {\n    hindi_lyrics: mockHindi,\n    english_lyrics: mockEnglish,\n    style_tags: styles,\n    success: true,\n    message: 'Lyrics generated successfully (mock mode)'\n  };\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAeO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAsB,MAAM,QAAQ,IAAI;QAE9C,IAAI,CAAC,KAAK,UAAU,EAAE,QAAQ;YAC5B,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,uCAAuC;QACvC,MAAM,aAAa,QAAQ,GAAG,CAAC,WAAW,IAAI;QAE9C,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,WAAW,gBAAgB,CAAC,EAAE;gBAC5D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,YAAY,KAAK,UAAU;oBAC3B,kBAAkB,KAAK,gBAAgB,IAAI;gBAC7C;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,SAAS,MAAM,EAAE;YACrE;YAEA,MAAM,SAAyB,MAAM,SAAS,IAAI;YAClD,OAAO,gJAAY,CAAC,IAAI,CAAC;QAE3B,EAAE,OAAO,cAAc;YACrB,QAAQ,KAAK,CAAC,8BAA8B;YAE5C,0DAA0D;YAC1D,MAAM,eAAe,mBAAmB,KAAK,UAAU,EAAE,KAAK,gBAAgB;YAC9E,OAAO,gJAAY,CAAC,IAAI,CAAC;QAC3B;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,SAAS,mBAAmB,SAAiB,EAAE,eAAwB;IACrE,gEAAgE;IAChE,MAAM,QAAQ,UAAU,WAAW;IAEnC,IAAI,YAAY;IAChB,IAAI,cAAc;IAClB,IAAI,SAAmB,EAAE;IAEzB,yCAAyC;IACzC,IAAI,MAAM,QAAQ,CAAC,aAAa,MAAM,QAAQ,CAAC,WAAW,MAAM,QAAQ,CAAC,YAAY;QACnF,SAAS;YAAC;YAAY;YAAY;SAAS;QAC3C,YAAY,CAAC;;;;;;;;mBAQE,CAAC;QAEhB,cAAc,CAAC;;;;;;;;4BAQS,CAAC;IAC3B,OAAO,IAAI,MAAM,QAAQ,CAAC,WAAW,MAAM,QAAQ,CAAC,YAAY,MAAM,QAAQ,CAAC,YAAY;QACzF,SAAS;YAAC;YAAa;YAAY;SAAc;QACjD,YAAY,CAAC;;;;;;;;2BAQU,CAAC;QAExB,cAAc,CAAC;;;;;;;;+BAQY,CAAC;IAC9B,OAAO;QACL,SAAS;YAAC;YAAY;YAAa;SAAS;QAC5C,YAAY,CAAC;;;;;;;;;;;qBAWI,CAAC;QAElB,cAAc,CAAC;;;;;;;;;;;mBAWA,CAAC;IAClB;IAEA,iCAAiC;IACjC,IAAI,iBAAiB;QACnB,MAAM,aAAa,gBAAgB,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,GAAG,WAAW;QAC3E,SAAS;eAAI,IAAI,IAAI;mBAAI;mBAAW;aAAW;SAAE;IACnD;IAEA,OAAO;QACL,cAAc;QACd,gBAAgB;QAChB,YAAY;QACZ,SAAS;QACT,SAAS;IACX;AACF", "debugId": null}}]}