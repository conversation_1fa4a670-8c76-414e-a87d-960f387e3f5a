module.exports = [
"[project]/.next-internal/server/app/api/generate/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[project]/src/app/api/generate/lyrics-helpers.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

// Helper functions for generating personalized lyrics
__turbopack_context__.s([
    "extractStoryElements",
    ()=>extractStoryElements,
    "generateEnglishLyrics",
    ()=>generateEnglishLyrics,
    "generateHindiLyrics",
    ()=>generateHindiLyrics,
    "getStyleTags",
    ()=>getStyleTags
]);
function extractStoryElements(story) {
    const elements = {
        place: "",
        activity: "",
        emotion: "",
        time: "",
        weather: ""
    };
    // Places
    if (story.includes("coffee") || story.includes("cafe")) elements.place = "coffee shop";
    else if (story.includes("library") || story.includes("book")) elements.place = "library";
    else if (story.includes("park") || story.includes("garden")) elements.place = "park";
    else if (story.includes("beach") || story.includes("ocean")) elements.place = "beach";
    else if (story.includes("school") || story.includes("college")) elements.place = "school";
    else if (story.includes("work") || story.includes("office")) elements.place = "workplace";
    // Activities
    if (story.includes("reading") || story.includes("book")) elements.activity = "reading";
    else if (story.includes("music") || story.includes("song")) elements.activity = "music";
    else if (story.includes("dance") || story.includes("dancing")) elements.activity = "dancing";
    else if (story.includes("walk") || story.includes("walking")) elements.activity = "walking";
    else if (story.includes("talk") || story.includes("conversation")) elements.activity = "talking";
    // Weather/Time
    if (story.includes("rain") || story.includes("rainy")) elements.weather = "rain";
    else if (story.includes("sunny") || story.includes("sunshine")) elements.weather = "sunny";
    else if (story.includes("evening") || story.includes("sunset")) elements.time = "evening";
    else if (story.includes("morning") || story.includes("dawn")) elements.time = "morning";
    return elements;
}
function generateHindiLyrics(story, elements) {
    let lyrics = "";
    // Verse 1 - Meeting/Beginning
    if (elements.place === "coffee shop") {
        lyrics += `कॉफी की खुशबू में, तेरा प्यार मिला
किताबों के बीच में, दिल का करार मिला
पहली नज़र में ही, जाना था मैंने
ये इश्क़ हमारा, दीवाना था मैंने

`;
    } else if (elements.weather === "rain") {
        lyrics += `बारिश की बूंदों में, तेरी याद आई
हर बूंद के साथ, मेरी दुआ आई
भीगे मौसम में, तेरा साथ चाहिए
इस दिल को बस, तेरा प्यार चाहिए

`;
    } else if (elements.place === "library") {
        lyrics += `किताबों की दुनिया में, तुझसे मुलाकात हुई
शब्दों के बीच में, मेरी बात हुई
चुपचाप पढ़ते हुए, नज़रें मिल गईं
दिल की कहानी, शुरू हो गई

`;
    } else {
        lyrics += `तेरे साथ चलना है मुझे
हर राह में, हर मोड़ पर
दिल की बात कहना है मुझे
तेरे कानों में, धीरे से

`;
    }
    // Chorus
    lyrics += `प्यार की ये कहानी है
जो शुरू हुई तुझसे मिलकर
हर दिन नया लगता है
जब तू पास होता है

`;
    // Verse 2 - Development of relationship
    if (elements.activity === "talking") {
        lyrics += `घंटों बात करते रहे हम
सपनों की, उम्मीदों की
हर शब्द में छुपा था प्यार
दिल की गहराइयों की

`;
    } else if (elements.activity === "walking") {
        lyrics += `साथ साथ चलते रहे हम
हर रास्ते पर, हर मोड़ पर
कदमों की आवाज़ में
प्यार की धुन सुनाई दी

`;
    } else {
        lyrics += `तेरे साथ बिताए हर लम्हे
सुनहरे ख्वाबों से भी प्यारे हैं
दिल की बात कहने को
शब्द भी कम लगते हैं

`;
    }
    // Bridge
    lyrics += `तू है तो मैं हूँ, मैं हूँ तो तू है
ये रिश्ता अनमोल है
तेरे बिना ये दुनिया
बिल्कुल अधूरी सी लगती है

`;
    // Final verse
    lyrics += `तेरे बिना अधूरा हूँ मैं
तेरे साथ पूरा हूँ मैं
ये प्यार की कहानी है
जो लिखी है हमने साथ में

हमेशा के लिए, हमेशा के लिए
तेरे साथ चलना है मुझे
हमेशा के लिए, हमेशा के लिए
तेरे प्यार में खोना है मुझे`;
    return lyrics;
}
function generateEnglishLyrics(story, elements) {
    let lyrics = "";
    // Verse 1 - Meeting/Beginning
    if (elements.place === "coffee shop") {
        lyrics += `In the fragrance of coffee, I found your love
Among the books and dreams, my heart found peace above
At first sight I knew, this was meant to be
This crazy love of ours, wild and free

`;
    } else if (elements.weather === "rain") {
        lyrics += `In the raindrops falling, I remembered you
With every drop that fell, my prayers came true
In this stormy weather, I need you by my side
This heart just needs your love, to be my guide

`;
    } else if (elements.place === "library") {
        lyrics += `In the world of books, we first met
Among the stories, our hearts were set
Reading quietly, our eyes did meet
That's when our love story became complete

`;
    } else {
        lyrics += `I want to walk with you, on every path we find
Through every twist and turn, with you I'll never mind
I want to tell you softly, what's hidden in my heart
These words of love I carry, right from the very start

`;
    }
    // Chorus
    lyrics += `This is our story of love
That began when I met you
Every day feels brand new
When you're here, when you're true

`;
    // Verse 2 - Development of relationship
    if (elements.activity === "talking") {
        lyrics += `We talked for hours about our dreams
About hopes and future schemes
Every word was filled with care
Love was floating in the air

`;
    } else if (elements.activity === "walking") {
        lyrics += `Walking together side by side
On every path, with you as my guide
The sound of our steps in harmony
Created love's sweet melody

`;
    } else {
        lyrics += `Every moment spent with you
Is more precious than morning dew
To express what's in my heart
Even words don't know where to start

`;
    }
    // Bridge
    lyrics += `You are here so I exist, I am here so you persist
This bond is precious and true
Without you this world feels
Incomplete and empty too

`;
    // Final verse
    lyrics += `Without you I'm incomplete
With you my life is sweet
This is our love story true
Written by me and you

Forever and always, forever and always
I want to walk with you
Forever and always, forever and always
In your love I want to stay true`;
    return lyrics;
}
function getStyleTags(story, stylePreference) {
    let styles = [];
    // Auto-detect styles based on story content
    if (story.includes("coffee") || story.includes("book") || story.includes("quiet")) {
        styles.push("acoustic", "intimate", "soft");
    }
    if (story.includes("rain") || story.includes("storm")) {
        styles.push("emotional", "melancholic", "atmospheric");
    }
    if (story.includes("dance") || story.includes("party") || story.includes("celebration")) {
        styles.push("upbeat", "joyful", "energetic");
    }
    if (story.includes("dream") || story.includes("fantasy") || story.includes("imagine")) {
        styles.push("dreamy", "ethereal", "ambient");
    }
    if (story.includes("beach") || story.includes("ocean") || story.includes("sunset")) {
        styles.push("chill", "relaxed", "tropical");
    }
    if (story.includes("school") || story.includes("college") || story.includes("young")) {
        styles.push("nostalgic", "youthful", "innocent");
    }
    // Always include romantic as base
    if (!styles.includes("romantic")) {
        styles.unshift("romantic");
    }
    // Add user preferences
    if (stylePreference) {
        const userStyles = stylePreference.split(",").map((s)=>s.trim().toLowerCase());
        styles = [
            ...new Set([
                ...styles,
                ...userStyles
            ])
        ];
    }
    // Limit to 4 styles for Suno compatibility
    return styles.slice(0, 4);
}
}),
"[project]/src/app/api/generate/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "POST",
    ()=>POST
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$api$2f$generate$2f$lyrics$2d$helpers$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/api/generate/lyrics-helpers.ts [app-route] (ecmascript)");
;
;
async function POST(request) {
    try {
        const body = await request.json();
        if (!body.love_story?.trim()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: "Love story is required"
            }, {
                status: 400
            });
        }
        // Try to connect to the Python backend
        const backendUrl = process.env.BACKEND_URL || "http://localhost:8000";
        try {
            const response = await fetch(`${backendUrl}/generate-lyrics`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    love_story: body.love_story,
                    style_preference: body.style_preference || "",
                    language: body.language || "hindi"
                })
            });
            if (!response.ok) {
                throw new Error(`Backend responded with status: ${response.status}`);
            }
            const result = await response.json();
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(result);
        } catch (backendError) {
            console.error("Backend connection failed:", backendError);
            // Fallback to mock response when backend is not available
            const mockResponse = generateMockLyrics(body.love_story, body.style_preference, body.language);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(mockResponse);
        }
    } catch (error) {
        console.error("Error in generate API:", error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: "Internal server error"
        }, {
            status: 500
        });
    }
}
function generateMockLyrics(loveStory, stylePreference, language) {
    // Extract themes and keywords from the love story
    const story = loveStory.toLowerCase();
    const selectedLang = language || "hindi";
    // Extract key elements from the story
    const storyElements = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$api$2f$generate$2f$lyrics$2d$helpers$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["extractStoryElements"])(story);
    let styles = [];
    let generatedLyrics = "";
    // Generate lyrics based on story themes and selected language
    if (selectedLang === "hindi") {
        generatedLyrics = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$api$2f$generate$2f$lyrics$2d$helpers$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateHindiLyrics"])(story, storyElements);
    } else {
        generatedLyrics = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$api$2f$generate$2f$lyrics$2d$helpers$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateEnglishLyrics"])(story, storyElements);
    }
    styles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$api$2f$generate$2f$lyrics$2d$helpers$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getStyleTags"])(story, stylePreference);
    return {
        lyrics: generatedLyrics,
        language: selectedLang,
        style_tags: styles,
        success: true,
        message: "Lyrics generated successfully (mock mode)"
    };
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__419d409b._.js.map