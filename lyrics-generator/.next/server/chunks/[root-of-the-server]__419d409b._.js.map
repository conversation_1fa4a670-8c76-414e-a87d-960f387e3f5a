{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Amor%20Business/Code/lyrics-generator/src/app/api/generate/lyrics-helpers.ts"], "sourcesContent": ["// Helper functions for generating personalized lyrics\n\nexport function extractStoryElements(story: string) {\n  const elements = {\n    place: \"\",\n    activity: \"\",\n    emotion: \"\",\n    time: \"\",\n    weather: \"\",\n  };\n\n  // Places\n  if (story.includes(\"coffee\") || story.includes(\"cafe\"))\n    elements.place = \"coffee shop\";\n  else if (story.includes(\"library\") || story.includes(\"book\"))\n    elements.place = \"library\";\n  else if (story.includes(\"park\") || story.includes(\"garden\"))\n    elements.place = \"park\";\n  else if (story.includes(\"beach\") || story.includes(\"ocean\"))\n    elements.place = \"beach\";\n  else if (story.includes(\"school\") || story.includes(\"college\"))\n    elements.place = \"school\";\n  else if (story.includes(\"work\") || story.includes(\"office\"))\n    elements.place = \"workplace\";\n\n  // Activities\n  if (story.includes(\"reading\") || story.includes(\"book\"))\n    elements.activity = \"reading\";\n  else if (story.includes(\"music\") || story.includes(\"song\"))\n    elements.activity = \"music\";\n  else if (story.includes(\"dance\") || story.includes(\"dancing\"))\n    elements.activity = \"dancing\";\n  else if (story.includes(\"walk\") || story.includes(\"walking\"))\n    elements.activity = \"walking\";\n  else if (story.includes(\"talk\") || story.includes(\"conversation\"))\n    elements.activity = \"talking\";\n\n  // Weather/Time\n  if (story.includes(\"rain\") || story.includes(\"rainy\"))\n    elements.weather = \"rain\";\n  else if (story.includes(\"sunny\") || story.includes(\"sunshine\"))\n    elements.weather = \"sunny\";\n  else if (story.includes(\"evening\") || story.includes(\"sunset\"))\n    elements.time = \"evening\";\n  else if (story.includes(\"morning\") || story.includes(\"dawn\"))\n    elements.time = \"morning\";\n\n  return elements;\n}\n\nexport function generateHindiLyrics(story: string, elements: any): string {\n  let lyrics = \"\";\n\n  // Verse 1 - Meeting/Beginning\n  if (elements.place === \"coffee shop\") {\n    lyrics += `कॉफी की खुशबू में, तेरा प्यार मिला\nकिताबों के बीच में, दिल का करार मिला\nपहली नज़र में ही, जाना था मैंने\nये इश्क़ हमारा, दीवाना था मैंने\n\n`;\n  } else if (elements.weather === \"rain\") {\n    lyrics += `बारिश की बूंदों में, तेरी याद आई\nहर बूंद के साथ, मेरी दुआ आई\nभीगे मौसम में, तेरा साथ चाहिए\nइस दिल को बस, तेरा प्यार चाहिए\n\n`;\n  } else if (elements.place === \"library\") {\n    lyrics += `किताबों की दुनिया में, तुझसे मुलाकात हुई\nशब्दों के बीच में, मेरी बात हुई\nचुपचाप पढ़ते हुए, नज़रें मिल गईं\nदिल की कहानी, शुरू हो गई\n\n`;\n  } else {\n    lyrics += `तेरे साथ चलना है मुझे\nहर राह में, हर मोड़ पर\nदिल की बात कहना है मुझे\nतेरे कानों में, धीरे से\n\n`;\n  }\n\n  // Chorus\n  lyrics += `प्यार की ये कहानी है\nजो शुरू हुई तुझसे मिलकर\nहर दिन नया लगता है\nजब तू पास होता है\n\n`;\n\n  // Verse 2 - Development of relationship\n  if (elements.activity === \"talking\") {\n    lyrics += `घंटों बात करते रहे हम\nसपनों की, उम्मीदों की\nहर शब्द में छुपा था प्यार\nदिल की गहराइयों की\n\n`;\n  } else if (elements.activity === \"walking\") {\n    lyrics += `साथ साथ चलते रहे हम\nहर रास्ते पर, हर मोड़ पर\nकदमों की आवाज़ में\nप्यार की धुन सुनाई दी\n\n`;\n  } else {\n    lyrics += `तेरे साथ बिताए हर लम्हे\nसुनहरे ख्वाबों से भी प्यारे हैं\nदिल की बात कहने को\nशब्द भी कम लगते हैं\n\n`;\n  }\n\n  // Bridge\n  lyrics += `तू है तो मैं हूँ, मैं हूँ तो तू है\nये रिश्ता अनमोल है\nतेरे बिना ये दुनिया\nबिल्कुल अधूरी सी लगती है\n\n`;\n\n  // Final verse\n  lyrics += `तेरे बिना अधूरा हूँ मैं\nतेरे साथ पूरा हूँ मैं\nये प्यार की कहानी है\nजो लिखी है हमने साथ में\n\nहमेशा के लिए, हमेशा के लिए\nतेरे साथ चलना है मुझे\nहमेशा के लिए, हमेशा के लिए\nतेरे प्यार में खोना है मुझे`;\n\n  return lyrics;\n}\n\nexport function generateEnglishLyrics(story: string, elements: any): string {\n  let lyrics = \"\";\n\n  // Verse 1 - Meeting/Beginning\n  if (elements.place === \"coffee shop\") {\n    lyrics += `In the fragrance of coffee, I found your love\nAmong the books and dreams, my heart found peace above\nAt first sight I knew, this was meant to be\nThis crazy love of ours, wild and free\n\n`;\n  } else if (elements.weather === \"rain\") {\n    lyrics += `In the raindrops falling, I remembered you\nWith every drop that fell, my prayers came true\nIn this stormy weather, I need you by my side\nThis heart just needs your love, to be my guide\n\n`;\n  } else if (elements.place === \"library\") {\n    lyrics += `In the world of books, we first met\nAmong the stories, our hearts were set\nReading quietly, our eyes did meet\nThat's when our love story became complete\n\n`;\n  } else {\n    lyrics += `I want to walk with you, on every path we find\nThrough every twist and turn, with you I'll never mind\nI want to tell you softly, what's hidden in my heart\nThese words of love I carry, right from the very start\n\n`;\n  }\n\n  // Chorus\n  lyrics += `This is our story of love\nThat began when I met you\nEvery day feels brand new\nWhen you're here, when you're true\n\n`;\n\n  // Verse 2 - Development of relationship\n  if (elements.activity === \"talking\") {\n    lyrics += `We talked for hours about our dreams\nAbout hopes and future schemes\nEvery word was filled with care\nLove was floating in the air\n\n`;\n  } else if (elements.activity === \"walking\") {\n    lyrics += `Walking together side by side\nOn every path, with you as my guide\nThe sound of our steps in harmony\nCreated love's sweet melody\n\n`;\n  } else {\n    lyrics += `Every moment spent with you\nIs more precious than morning dew\nTo express what's in my heart\nEven words don't know where to start\n\n`;\n  }\n\n  // Bridge\n  lyrics += `You are here so I exist, I am here so you persist\nThis bond is precious and true\nWithout you this world feels\nIncomplete and empty too\n\n`;\n\n  // Final verse\n  lyrics += `Without you I'm incomplete\nWith you my life is sweet\nThis is our love story true\nWritten by me and you\n\nForever and always, forever and always\nI want to walk with you\nForever and always, forever and always\nIn your love I want to stay true`;\n\n  return lyrics;\n}\n\nexport function getStyleTags(\n  story: string,\n  stylePreference?: string\n): string[] {\n  let styles: string[] = [];\n\n  // Auto-detect styles based on story content\n  if (\n    story.includes(\"coffee\") ||\n    story.includes(\"book\") ||\n    story.includes(\"quiet\")\n  ) {\n    styles.push(\"acoustic\", \"intimate\", \"soft\");\n  }\n  if (story.includes(\"rain\") || story.includes(\"storm\")) {\n    styles.push(\"emotional\", \"melancholic\", \"atmospheric\");\n  }\n  if (\n    story.includes(\"dance\") ||\n    story.includes(\"party\") ||\n    story.includes(\"celebration\")\n  ) {\n    styles.push(\"upbeat\", \"joyful\", \"energetic\");\n  }\n  if (\n    story.includes(\"dream\") ||\n    story.includes(\"fantasy\") ||\n    story.includes(\"imagine\")\n  ) {\n    styles.push(\"dreamy\", \"ethereal\", \"ambient\");\n  }\n  if (\n    story.includes(\"beach\") ||\n    story.includes(\"ocean\") ||\n    story.includes(\"sunset\")\n  ) {\n    styles.push(\"chill\", \"relaxed\", \"tropical\");\n  }\n  if (\n    story.includes(\"school\") ||\n    story.includes(\"college\") ||\n    story.includes(\"young\")\n  ) {\n    styles.push(\"nostalgic\", \"youthful\", \"innocent\");\n  }\n\n  // Always include romantic as base\n  if (!styles.includes(\"romantic\")) {\n    styles.unshift(\"romantic\");\n  }\n\n  // Add user preferences\n  if (stylePreference) {\n    const userStyles = stylePreference\n      .split(\",\")\n      .map((s) => s.trim().toLowerCase());\n    styles = [...new Set([...styles, ...userStyles])];\n  }\n\n  // Limit to 4 styles for Suno compatibility\n  return styles.slice(0, 4);\n}\n"], "names": [], "mappings": "AAAA,sDAAsD;;;;;;;;;;;AAE/C,SAAS,qBAAqB,KAAa;IAChD,MAAM,WAAW;QACf,OAAO;QACP,UAAU;QACV,SAAS;QACT,MAAM;QACN,SAAS;IACX;IAEA,SAAS;IACT,IAAI,MAAM,QAAQ,CAAC,aAAa,MAAM,QAAQ,CAAC,SAC7C,SAAS,KAAK,GAAG;SACd,IAAI,MAAM,QAAQ,CAAC,cAAc,MAAM,QAAQ,CAAC,SACnD,SAAS,KAAK,GAAG;SACd,IAAI,MAAM,QAAQ,CAAC,WAAW,MAAM,QAAQ,CAAC,WAChD,SAAS,KAAK,GAAG;SACd,IAAI,MAAM,QAAQ,CAAC,YAAY,MAAM,QAAQ,CAAC,UACjD,SAAS,KAAK,GAAG;SACd,IAAI,MAAM,QAAQ,CAAC,aAAa,MAAM,QAAQ,CAAC,YAClD,SAAS,KAAK,GAAG;SACd,IAAI,MAAM,QAAQ,CAAC,WAAW,MAAM,QAAQ,CAAC,WAChD,SAAS,KAAK,GAAG;IAEnB,aAAa;IACb,IAAI,MAAM,QAAQ,CAAC,cAAc,MAAM,QAAQ,CAAC,SAC9C,SAAS,QAAQ,GAAG;SACjB,IAAI,MAAM,QAAQ,CAAC,YAAY,MAAM,QAAQ,CAAC,SACjD,SAAS,QAAQ,GAAG;SACjB,IAAI,MAAM,QAAQ,CAAC,YAAY,MAAM,QAAQ,CAAC,YACjD,SAAS,QAAQ,GAAG;SACjB,IAAI,MAAM,QAAQ,CAAC,WAAW,MAAM,QAAQ,CAAC,YAChD,SAAS,QAAQ,GAAG;SACjB,IAAI,MAAM,QAAQ,CAAC,WAAW,MAAM,QAAQ,CAAC,iBAChD,SAAS,QAAQ,GAAG;IAEtB,eAAe;IACf,IAAI,MAAM,QAAQ,CAAC,WAAW,MAAM,QAAQ,CAAC,UAC3C,SAAS,OAAO,GAAG;SAChB,IAAI,MAAM,QAAQ,CAAC,YAAY,MAAM,QAAQ,CAAC,aACjD,SAAS,OAAO,GAAG;SAChB,IAAI,MAAM,QAAQ,CAAC,cAAc,MAAM,QAAQ,CAAC,WACnD,SAAS,IAAI,GAAG;SACb,IAAI,MAAM,QAAQ,CAAC,cAAc,MAAM,QAAQ,CAAC,SACnD,SAAS,IAAI,GAAG;IAElB,OAAO;AACT;AAEO,SAAS,oBAAoB,KAAa,EAAE,QAAa;IAC9D,IAAI,SAAS;IAEb,8BAA8B;IAC9B,IAAI,SAAS,KAAK,KAAK,eAAe;QACpC,UAAU,CAAC;;;;;AAKf,CAAC;IACC,OAAO,IAAI,SAAS,OAAO,KAAK,QAAQ;QACtC,UAAU,CAAC;;;;;AAKf,CAAC;IACC,OAAO,IAAI,SAAS,KAAK,KAAK,WAAW;QACvC,UAAU,CAAC;;;;;AAKf,CAAC;IACC,OAAO;QACL,UAAU,CAAC;;;;;AAKf,CAAC;IACC;IAEA,SAAS;IACT,UAAU,CAAC;;;;;AAKb,CAAC;IAEC,wCAAwC;IACxC,IAAI,SAAS,QAAQ,KAAK,WAAW;QACnC,UAAU,CAAC;;;;;AAKf,CAAC;IACC,OAAO,IAAI,SAAS,QAAQ,KAAK,WAAW;QAC1C,UAAU,CAAC;;;;;AAKf,CAAC;IACC,OAAO;QACL,UAAU,CAAC;;;;;AAKf,CAAC;IACC;IAEA,SAAS;IACT,UAAU,CAAC;;;;;AAKb,CAAC;IAEC,cAAc;IACd,UAAU,CAAC;;;;;;;;2BAQc,CAAC;IAE1B,OAAO;AACT;AAEO,SAAS,sBAAsB,KAAa,EAAE,QAAa;IAChE,IAAI,SAAS;IAEb,8BAA8B;IAC9B,IAAI,SAAS,KAAK,KAAK,eAAe;QACpC,UAAU,CAAC;;;;;AAKf,CAAC;IACC,OAAO,IAAI,SAAS,OAAO,KAAK,QAAQ;QACtC,UAAU,CAAC;;;;;AAKf,CAAC;IACC,OAAO,IAAI,SAAS,KAAK,KAAK,WAAW;QACvC,UAAU,CAAC;;;;;AAKf,CAAC;IACC,OAAO;QACL,UAAU,CAAC;;;;;AAKf,CAAC;IACC;IAEA,SAAS;IACT,UAAU,CAAC;;;;;AAKb,CAAC;IAEC,wCAAwC;IACxC,IAAI,SAAS,QAAQ,KAAK,WAAW;QACnC,UAAU,CAAC;;;;;AAKf,CAAC;IACC,OAAO,IAAI,SAAS,QAAQ,KAAK,WAAW;QAC1C,UAAU,CAAC;;;;;AAKf,CAAC;IACC,OAAO;QACL,UAAU,CAAC;;;;;AAKf,CAAC;IACC;IAEA,SAAS;IACT,UAAU,CAAC;;;;;AAKb,CAAC;IAEC,cAAc;IACd,UAAU,CAAC;;;;;;;;gCAQmB,CAAC;IAE/B,OAAO;AACT;AAEO,SAAS,aACd,KAAa,EACb,eAAwB;IAExB,IAAI,SAAmB,EAAE;IAEzB,4CAA4C;IAC5C,IACE,MAAM,QAAQ,CAAC,aACf,MAAM,QAAQ,CAAC,WACf,MAAM,QAAQ,CAAC,UACf;QACA,OAAO,IAAI,CAAC,YAAY,YAAY;IACtC;IACA,IAAI,MAAM,QAAQ,CAAC,WAAW,MAAM,QAAQ,CAAC,UAAU;QACrD,OAAO,IAAI,CAAC,aAAa,eAAe;IAC1C;IACA,IACE,MAAM,QAAQ,CAAC,YACf,MAAM,QAAQ,CAAC,YACf,MAAM,QAAQ,CAAC,gBACf;QACA,OAAO,IAAI,CAAC,UAAU,UAAU;IAClC;IACA,IACE,MAAM,QAAQ,CAAC,YACf,MAAM,QAAQ,CAAC,cACf,MAAM,QAAQ,CAAC,YACf;QACA,OAAO,IAAI,CAAC,UAAU,YAAY;IACpC;IACA,IACE,MAAM,QAAQ,CAAC,YACf,MAAM,QAAQ,CAAC,YACf,MAAM,QAAQ,CAAC,WACf;QACA,OAAO,IAAI,CAAC,SAAS,WAAW;IAClC;IACA,IACE,MAAM,QAAQ,CAAC,aACf,MAAM,QAAQ,CAAC,cACf,MAAM,QAAQ,CAAC,UACf;QACA,OAAO,IAAI,CAAC,aAAa,YAAY;IACvC;IAEA,kCAAkC;IAClC,IAAI,CAAC,OAAO,QAAQ,CAAC,aAAa;QAChC,OAAO,OAAO,CAAC;IACjB;IAEA,uBAAuB;IACvB,IAAI,iBAAiB;QACnB,MAAM,aAAa,gBAChB,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI,GAAG,WAAW;QAClC,SAAS;eAAI,IAAI,IAAI;mBAAI;mBAAW;aAAW;SAAE;IACnD;IAEA,2CAA2C;IAC3C,OAAO,OAAO,KAAK,CAAC,GAAG;AACzB", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Amor%20Business/Code/lyrics-generator/src/app/api/generate/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport {\n  generateHindiLyrics,\n  generateEnglishLyrics,\n  getStyleTags,\n  extractStoryElements,\n} from \"./lyrics-helpers\";\n\ninterface LyricsRequest {\n  love_story: string;\n  style_preference?: string;\n  language?: string;\n}\n\ninterface LyricsResponse {\n  lyrics: string;\n  language: string;\n  style_tags: string[];\n  success: boolean;\n  message: string;\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body: LyricsRequest = await request.json();\n\n    if (!body.love_story?.trim()) {\n      return NextResponse.json(\n        { error: \"Love story is required\" },\n        { status: 400 }\n      );\n    }\n\n    // Try to connect to the Python backend\n    const backendUrl = process.env.BACKEND_URL || \"http://localhost:8000\";\n\n    try {\n      const response = await fetch(`${backendUrl}/generate-lyrics`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({\n          love_story: body.love_story,\n          style_preference: body.style_preference || \"\",\n          language: body.language || \"hindi\",\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Backend responded with status: ${response.status}`);\n      }\n\n      const result: LyricsResponse = await response.json();\n      return NextResponse.json(result);\n    } catch (backendError) {\n      console.error(\"Backend connection failed:\", backendError);\n\n      // Fallback to mock response when backend is not available\n      const mockResponse = generateMockLyrics(\n        body.love_story,\n        body.style_preference,\n        body.language\n      );\n      return NextResponse.json(mockResponse);\n    }\n  } catch (error) {\n    console.error(\"Error in generate API:\", error);\n    return NextResponse.json(\n      { error: \"Internal server error\" },\n      { status: 500 }\n    );\n  }\n}\n\nfunction generateMockLyrics(\n  loveStory: string,\n  stylePreference?: string,\n  language?: string\n): LyricsResponse {\n  // Extract themes and keywords from the love story\n  const story = loveStory.toLowerCase();\n  const selectedLang = language || \"hindi\";\n\n  // Extract key elements from the story\n  const storyElements = extractStoryElements(story);\n  let styles: string[] = [];\n  let generatedLyrics = \"\";\n\n  // Generate lyrics based on story themes and selected language\n  if (selectedLang === \"hindi\") {\n    generatedLyrics = generateHindiLyrics(story, storyElements);\n  } else {\n    generatedLyrics = generateEnglishLyrics(story, storyElements);\n  }\n\n  styles = getStyleTags(story, stylePreference);\n\n  return {\n    lyrics: generatedLyrics,\n    language: selectedLang,\n    style_tags: styles,\n    success: true,\n    message: \"Lyrics generated successfully (mock mode)\",\n  };\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAqBO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAsB,MAAM,QAAQ,IAAI;QAE9C,IAAI,CAAC,KAAK,UAAU,EAAE,QAAQ;YAC5B,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,uCAAuC;QACvC,MAAM,aAAa,QAAQ,GAAG,CAAC,WAAW,IAAI;QAE9C,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,WAAW,gBAAgB,CAAC,EAAE;gBAC5D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,YAAY,KAAK,UAAU;oBAC3B,kBAAkB,KAAK,gBAAgB,IAAI;oBAC3C,UAAU,KAAK,QAAQ,IAAI;gBAC7B;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,SAAS,MAAM,EAAE;YACrE;YAEA,MAAM,SAAyB,MAAM,SAAS,IAAI;YAClD,OAAO,gJAAY,CAAC,IAAI,CAAC;QAC3B,EAAE,OAAO,cAAc;YACrB,QAAQ,KAAK,CAAC,8BAA8B;YAE5C,0DAA0D;YAC1D,MAAM,eAAe,mBACnB,KAAK,UAAU,EACf,KAAK,gBAAgB,EACrB,KAAK,QAAQ;YAEf,OAAO,gJAAY,CAAC,IAAI,CAAC;QAC3B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,SAAS,mBACP,SAAiB,EACjB,eAAwB,EACxB,QAAiB;IAEjB,kDAAkD;IAClD,MAAM,QAAQ,UAAU,WAAW;IACnC,MAAM,eAAe,YAAY;IAEjC,sCAAsC;IACtC,MAAM,gBAAgB,IAAA,4KAAoB,EAAC;IAC3C,IAAI,SAAmB,EAAE;IACzB,IAAI,kBAAkB;IAEtB,8DAA8D;IAC9D,IAAI,iBAAiB,SAAS;QAC5B,kBAAkB,IAAA,2KAAmB,EAAC,OAAO;IAC/C,OAAO;QACL,kBAAkB,IAAA,6KAAqB,EAAC,OAAO;IACjD;IAEA,SAAS,IAAA,oKAAY,EAAC,OAAO;IAE7B,OAAO;QACL,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,SAAS;QACT,SAAS;IACX;AACF", "debugId": null}}]}