var R=require("../../../chunks/[turbopack]_runtime.js")("server/app/api/generate/route.js")
R.c("server/chunks/node_modules_next_8031d30a._.js")
R.c("server/chunks/[root-of-the-server]__14ea26ec._.js")
R.m("[project]/.next-internal/server/app/api/generate/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/generate/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/generate/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
