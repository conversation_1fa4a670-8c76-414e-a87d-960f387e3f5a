# Deployment Guide - AI Lyrics Generator

This guide covers different deployment options for the AI Lyrics Generator system.

## 🏠 Local Development Setup

### Prerequisites
- Python 3.9+
- Node.js 18+
- 8GB+ RAM
- Git

### Step-by-Step Setup

1. **Clone and Setup**
```bash
git clone <your-repo>
cd lyrics-generator-project
```

2. **Backend Setup**
```bash
# Install Python dependencies
pip install -r requirements.txt

# Prepare the data and model
python3 analyze_data.py
python3 prepare_training_data.py

# Optional: Quick training demo
python3 quick_train.py
```

3. **Frontend Setup**
```bash
cd lyrics-generator
npm install
```

4. **Start Services**

Terminal 1 (Backend):
```bash
python3 backend_api.py
```

Terminal 2 (Frontend):
```bash
cd lyrics-generator
npm run dev
```

5. **Access the Application**
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Docs: http://localhost:8000/docs

## ☁️ Cloud Deployment Options

### Option 1: Vercel + Railway

**Frontend (Vercel):**
```bash
cd lyrics-generator
npm run build
vercel --prod
```

**Backend (Railway):**
1. Create a Railway account
2. Connect your GitHub repository
3. Deploy the Python backend
4. Set environment variables

**Environment Variables:**
```
BACKEND_URL=https://your-railway-app.railway.app
```

### Option 2: Docker Deployment

**Create Dockerfile for Backend:**
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["python", "backend_api.py"]
```

**Create Dockerfile for Frontend:**
```dockerfile
FROM node:18-alpine

WORKDIR /app
COPY lyrics-generator/package*.json ./
RUN npm install

COPY lyrics-generator/ .
RUN npm run build

EXPOSE 3000
CMD ["npm", "start"]
```

**Docker Compose:**
```yaml
version: '3.8'
services:
  backend:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./models:/app/models
  
  frontend:
    build: ./lyrics-generator
    ports:
      - "3000:3000"
    environment:
      - BACKEND_URL=http://backend:8000
    depends_on:
      - backend
```

### Option 3: AWS Deployment

**Backend (AWS Lambda + API Gateway):**
1. Package the model and dependencies
2. Create Lambda function
3. Set up API Gateway
4. Configure CORS

**Frontend (AWS S3 + CloudFront):**
```bash
cd lyrics-generator
npm run build
aws s3 sync out/ s3://your-bucket-name
```

## 🔧 Production Configuration

### Backend Optimizations

**1. Model Loading Optimization:**
```python
# In backend_api.py
import os
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Use model caching
@lru_cache(maxsize=1)
def load_model():
    return LyricsGenerator()
```

**2. API Rate Limiting:**
```python
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter

@app.post("/generate-lyrics")
@limiter.limit("5/minute")
async def generate_lyrics(request: Request, lyrics_request: LyricsRequest):
    # ... existing code
```

**3. Environment Variables:**
```bash
# .env file
MODEL_PATH=/path/to/your/model
MAX_WORKERS=4
LOG_LEVEL=INFO
CORS_ORIGINS=https://yourdomain.com
```

### Frontend Optimizations

**1. Build Optimization:**
```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'standalone',
  compress: true,
  images: {
    unoptimized: true
  }
}

module.exports = nextConfig
```

**2. Environment Variables:**
```bash
# .env.local
NEXT_PUBLIC_API_URL=https://your-api-domain.com
```

## 📊 Monitoring and Logging

### Backend Monitoring
```python
import logging
from fastapi import Request
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    logger.info(f"{request.method} {request.url} - {response.status_code} - {process_time:.2f}s")
    return response
```

### Health Checks
```python
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "model_loaded": model is not None,
        "memory_usage": psutil.virtual_memory().percent
    }
```

## 🔒 Security Considerations

### API Security
```python
from fastapi.security import HTTPBearer
from fastapi import Depends, HTTPException

security = HTTPBearer()

async def verify_token(token: str = Depends(security)):
    if token.credentials != "your-secret-token":
        raise HTTPException(status_code=401, detail="Invalid token")
    return token
```

### CORS Configuration
```python
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://yourdomain.com"],  # Specific domains only
    allow_credentials=True,
    allow_methods=["POST", "GET"],
    allow_headers=["*"],
)
```

## 📈 Scaling Considerations

### Horizontal Scaling
- Use load balancers (nginx, AWS ALB)
- Deploy multiple backend instances
- Implement session-less design

### Model Optimization
- Use model quantization for smaller memory footprint
- Implement model caching
- Consider using GPU instances for faster inference

### Database Integration (Optional)
```python
# For storing user requests and generated lyrics
from sqlalchemy import create_engine, Column, Integer, String, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

Base = declarative_base()

class GeneratedLyrics(Base):
    __tablename__ = "generated_lyrics"
    
    id = Column(Integer, primary_key=True)
    love_story = Column(Text)
    hindi_lyrics = Column(Text)
    english_lyrics = Column(Text)
    style_tags = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)
```

## 🚨 Troubleshooting

### Common Issues

**1. Model Loading Errors:**
```bash
# Check memory usage
free -h

# Check model files
ls -la ./lyrics_model_final/

# Test model loading
python3 -c "from backend_api import LyricsGenerator; LyricsGenerator()"
```

**2. CORS Issues:**
```javascript
// In frontend, check API calls
const response = await fetch(process.env.NEXT_PUBLIC_API_URL + '/generate-lyrics', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(data)
});
```

**3. Performance Issues:**
- Monitor CPU and memory usage
- Check model inference time
- Implement request queuing for high load

### Logs and Debugging
```bash
# Backend logs
tail -f backend.log

# Frontend logs
npm run dev -- --debug

# System resources
htop
nvidia-smi  # For GPU usage
```

## 📋 Deployment Checklist

- [ ] Environment variables configured
- [ ] Model files uploaded/accessible
- [ ] CORS settings configured
- [ ] Health checks implemented
- [ ] Logging configured
- [ ] Error handling tested
- [ ] Performance benchmarked
- [ ] Security measures in place
- [ ] Backup strategy defined
- [ ] Monitoring setup

## 🔄 CI/CD Pipeline

**GitHub Actions Example:**
```yaml
name: Deploy Lyrics Generator

on:
  push:
    branches: [main]

jobs:
  deploy-backend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Deploy to Railway
        run: railway deploy
        
  deploy-frontend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Deploy to Vercel
        run: vercel --prod --token ${{ secrets.VERCEL_TOKEN }}
```

---

For additional support, refer to the main README.md or create an issue in the repository.
