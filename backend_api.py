#!/usr/bin/env python3
"""
FastAPI Backend for Lyrics Generation
Serves the fine-tuned model and handles user requests
"""

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import torch
from transformers import <PERSON>Tokenizer, AutoModelForCausalLM
import json
import re
from typing import List, Optional
import uvicorn
import os

app = FastAPI(title="Lyrics Generator API", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables for model and tokenizer
model = None
tokenizer = None

class LyricsRequest(BaseModel):
    love_story: str
    style_preference: Optional[str] = ""
    language: Optional[str] = "hindi"

class LyricsResponse(BaseModel):
    lyrics: str
    language: str
    style_tags: List[str]
    success: bool
    message: str

class LyricsGenerator:
    def __init__(self, model_path="./lyrics_model_final"):
        """Initialize the lyrics generator with the fine-tuned model"""
        self.model_path = model_path
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.load_model()
        
        # Style mapping
        self.style_keywords = {
            'romantic': ['love', 'heart', 'romance', 'kiss', 'embrace'],
            'emotional': ['tears', 'cry', 'pain', 'joy', 'happiness'],
            'passionate': ['fire', 'burn', 'desire', 'passion', 'intense'],
            'dreamy': ['dream', 'fantasy', 'imagine', 'wish', 'hope'],
            'nostalgic': ['memory', 'remember', 'past', 'yesterday', 'time'],
            'upbeat': ['dance', 'celebrate', 'happy', 'joy', 'party'],
            'melancholic': ['sad', 'lonely', 'miss', 'apart', 'distance']
        }
        
    def load_model(self):
        """Load the fine-tuned model and tokenizer"""
        try:
            if os.path.exists(self.model_path):
                print(f"Loading fine-tuned model from {self.model_path}")
                self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
                self.model = AutoModelForCausalLM.from_pretrained(
                    self.model_path,
                    torch_dtype=torch.float32,
                    device_map="auto" if self.device == "cuda" else None
                )
            else:
                print("Fine-tuned model not found, using base model")
                model_name = "Qwen/Qwen2.5-1.5B-Instruct"
                self.tokenizer = AutoTokenizer.from_pretrained(model_name)
                self.model = AutoModelForCausalLM.from_pretrained(model_name)
                
                # Add special tokens
                special_tokens = {
                    "pad_token": "<pad>",
                    "additional_special_tokens": [
                        "<LOVE_STORY>", "</LOVE_STORY>",
                        "<LYRICS>", "</LYRICS>",
                        "<STYLE>", "</STYLE>",
                        "<HINDI>", "</HINDI>",
                        "<ENGLISH>", "</ENGLISH>"
                    ]
                }
                
                if self.tokenizer.pad_token is None:
                    self.tokenizer.add_special_tokens({"pad_token": "<pad>"})
                
                self.tokenizer.add_special_tokens(special_tokens)
                self.model.resize_token_embeddings(len(self.tokenizer))
            
            self.model.eval()
            print(f"Model loaded successfully on {self.device}")
            
        except Exception as e:
            print(f"Error loading model: {e}")
            raise
    
    def extract_style_tags(self, love_story: str, style_preference: str = "") -> List[str]:
        """Extract appropriate style tags based on the love story and preferences"""
        story_lower = love_story.lower()
        detected_styles = []
        
        # Check for style keywords in the story
        for style, keywords in self.style_keywords.items():
            if any(keyword in story_lower for keyword in keywords):
                detected_styles.append(style)
        
        # Add user preferences
        if style_preference:
            user_styles = [s.strip().lower() for s in style_preference.split(',')]
            detected_styles.extend(user_styles)
        
        # Remove duplicates and limit to 4 styles
        unique_styles = list(dict.fromkeys(detected_styles))[:4]
        
        # Default styles if none detected
        if not unique_styles:
            unique_styles = ['romantic', 'heartfelt']
        
        return unique_styles
    
    def generate_lyrics(self, love_story: str, style_preference: str = "", language: str = "hindi") -> dict:
        """Generate lyrics based on the love story"""
        try:
            # Extract style tags
            style_tags = self.extract_style_tags(love_story, style_preference)
            
            # Create prompt
            prompt = f"""<LOVE_STORY>{love_story}</LOVE_STORY>

<STYLE>{', '.join(style_tags)}</STYLE>

<LYRICS>
Hindi:"""

            # Tokenize input
            inputs = self.tokenizer.encode(prompt, return_tensors='pt')
            
            # Generate
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_length=inputs.shape[1] + 200,
                    num_return_sequences=1,
                    temperature=0.8,
                    do_sample=True,
                    pad_token_id=self.tokenizer.pad_token_id,
                    eos_token_id=self.tokenizer.eos_token_id,
                    repetition_penalty=1.2
                )
            
            # Decode generated text
            generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Extract lyrics from generated text
            lyrics = self.parse_generated_lyrics(generated_text, language)

            return {
                'lyrics': lyrics,
                'language': language,
                'style_tags': style_tags,
                'success': True,
                'message': 'Lyrics generated successfully'
            }
            
        except Exception as e:
            print(f"Error generating lyrics: {e}")
            return {
                'lyrics': '',
                'language': language,
                'style_tags': [],
                'success': False,
                'message': f'Error generating lyrics: {str(e)}'
            }
    
    def parse_generated_lyrics(self, generated_text: str, language: str = "hindi") -> str:
        """Parse the generated text to extract lyrics in the specified language"""
        try:
            # Find the lyrics section
            lyrics_start = generated_text.find('<LYRICS>')
            if lyrics_start == -1:
                lyrics_start = generated_text.find('Hindi:') if language == 'hindi' else generated_text.find('English:')

            if lyrics_start != -1:
                lyrics_section = generated_text[lyrics_start:]

                if language == 'hindi':
                    # Extract Hindi lyrics
                    hindi_match = re.search(r'Hindi:\s*(.*?)(?:English:|$)', lyrics_section, re.DOTALL)
                    lyrics = hindi_match.group(1).strip() if hindi_match else ""
                else:
                    # Extract English lyrics
                    english_match = re.search(r'English:\s*(.*?)(?:$|<)', lyrics_section, re.DOTALL)
                    lyrics = english_match.group(1).strip() if english_match else ""

                # Clean up the lyrics
                lyrics = self.clean_lyrics(lyrics)

                return lyrics if lyrics else self.get_fallback_lyrics(language)

            # Fallback: use mock lyrics if parsing fails
            return self.get_fallback_lyrics(language)

        except Exception as e:
            print(f"Error parsing lyrics: {e}")
            return self.get_fallback_lyrics(language)
    
    def clean_lyrics(self, lyrics: str) -> str:
        """Clean and format the lyrics"""
        if not lyrics:
            return ""
        
        # Remove extra whitespace and clean up
        lines = [line.strip() for line in lyrics.split('\n') if line.strip()]
        return '\n'.join(lines)
    
    def get_fallback_lyrics(self, language: str = "hindi") -> str:
        """Return fallback lyrics when generation fails"""
        if language == 'hindi':
            return """तेरे साथ चलना है मुझे
हर राह में, हर मोड़ पर
दिल की बात कहना है मुझे
तेरे कानों में, धीरे से

प्यार की ये कहानी है
जो शुरू हुई तुझसे मिलकर
हर दिन नया लगता है
जब तू पास होता है

तेरे बिना अधूरा हूँ मैं
तेरे साथ पूरा हूँ मैं"""
        else:
            return """I want to walk with you
On every path, at every turn
I want to tell you what's in my heart
In your ears, softly

This is a story of love
That began when I met you
Every day feels new
When you are near

Without you I'm incomplete
With you I am whole"""

# Initialize the generator
lyrics_generator = None

@app.on_event("startup")
async def startup_event():
    """Initialize the model on startup"""
    global lyrics_generator
    try:
        lyrics_generator = LyricsGenerator()
        print("Lyrics generator initialized successfully")
    except Exception as e:
        print(f"Failed to initialize lyrics generator: {e}")
        lyrics_generator = None

@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "Lyrics Generator API is running", "status": "healthy"}

@app.post("/generate-lyrics", response_model=LyricsResponse)
async def generate_lyrics(request: LyricsRequest):
    """Generate lyrics based on love story"""
    if not lyrics_generator:
        raise HTTPException(status_code=500, detail="Model not initialized")
    
    if not request.love_story.strip():
        raise HTTPException(status_code=400, detail="Love story cannot be empty")
    
    try:
        result = lyrics_generator.generate_lyrics(
            request.love_story,
            request.style_preference,
            request.language
        )
        
        return LyricsResponse(**result)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating lyrics: {str(e)}")

@app.get("/health")
async def health_check():
    """Detailed health check"""
    return {
        "status": "healthy",
        "model_loaded": lyrics_generator is not None,
        "device": lyrics_generator.device if lyrics_generator else "unknown"
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
